import 'package:base_app/core/widgets/app_map.dart';
import 'package:base_app/features/home/<USER>/models/place_detail_response.dart';
import 'package:flutter/material.dart';

class BuildBookTripWidget extends StatefulWidget {
  final PlaceDetailResponse? placeCurrentDetail;
  final PlaceDetailResponse? placeDestinationDetail;
  const BuildBookTripWidget({
    super.key,
    this.placeCurrentDetail,
    this.placeDestinationDetail,
  });

  @override
  State<BuildBookTripWidget> createState() => _BuildBookTripWidgetState();
}

class _BuildBookTripWidgetState extends State<BuildBookTripWidget> {

  @override
  Widget build(BuildContext context) {
    return AppMapbox(
      placeCurrentDetail: widget.placeCurrentDetail,
      placeDestinationDetail: widget.placeDestinationDetail,
    );
  }
}
